#!/usr/bin/env python3
"""
表情生成器测试脚本
用于验证各个组件的功能
"""

import os
import sys
from emoji_generator import EmojiGenerator, GenerationResult


def test_api_connection():
    """测试API连接"""
    print("\n🧪 测试1: API连接")
    print("-" * 40)
    
    try:
        generator = EmojiGenerator()
        print("✅ API密钥配置正确")
        return True
    except ValueError as e:
        print(f"❌ {e}")
        return False


def test_intent_understanding():
    """测试意图理解"""
    print("\n🧪 测试2: 意图理解代理")
    print("-" * 40)
    
    try:
        generator = EmojiGenerator()
        intent = generator.understand_intent("开心")
        
        if intent and len(intent) > 10:
            print("✅ 意图理解功能正常")
            print(f"   理解结果: {intent[:50]}...")
            return True
        else:
            print("❌ 意图理解结果异常")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def test_search():
    """测试搜索功能"""
    print("\n🧪 测试3: 联网检索")
    print("-" * 40)
    
    try:
        generator = EmojiGenerator()
        results = generator.search_and_enrich("快乐 幸福")
        
        if results and len(results) > 0:
            print("✅ 搜索功能正常")
            print(f"   搜索结果: {results[:50]}...")
            return True
        else:
            print("❌ 搜索结果为空")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def test_description_reconstruction():
    """测试描述重构"""
    print("\n🧪 测试4: 描述重构代理")
    print("-" * 40)
    
    try:
        generator = EmojiGenerator()
        description = generator.reconstruct_description(
            intent="表达快乐的情绪",
            search_results="快乐是一种积极的情绪",
            user_input="开心"
        )
        
        if description and len(description) > 10:
            print("✅ 描述重构功能正常")
            print(f"   重构结果: {description[:50]}...")
            return True
        else:
            print("❌ 描述重构结果异常")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def test_emoji_generation():
    """测试表情生成"""
    print("\n🧪 测试5: 表情生成代理")
    print("-" * 40)
    
    try:
        generator = EmojiGenerator()
        emoji = generator.generate_emoji("一个开心的笑脸")
        
        if emoji and len(emoji) > 0:
            print("✅ 表情生成功能正常")
            print(f"   生成表情: {emoji}")
            return True
        else:
            print("❌ 表情生成结果为空")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def test_evaluation():
    """测试评估功能"""
    print("\n🧪 测试6: 品鉴大师代理")
    print("-" * 40)
    
    try:
        generator = EmojiGenerator()
        score, feedback = generator.evaluate_emoji(
            emoji="😊",
            original_request="开心",
            description="一个开心的笑脸"
        )
        
        if 0 <= score <= 10 and feedback:
            print("✅ 评估功能正常")
            print(f"   评分: {score}/10")
            print(f"   反馈: {feedback[:50]}...")
            return True
        else:
            print("❌ 评估结果异常")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        return False


def test_full_generation():
    """测试完整生成流程"""
    print("\n🧪 测试7: 完整生成流程")
    print("-" * 40)
    
    try:
        generator = EmojiGenerator()
        result = generator.generate(
            user_input="开心",
            min_score=5.0,  # 测试时使用较低的分数要求
            max_iterations=2  # 减少迭代次数以加快测试
        )
        
        if isinstance(result, GenerationResult) and result.emoji:
            print("✅ 完整流程测试通过")
            print(f"   生成表情: {result.emoji}")
            print(f"   评分: {result.score}/10")
            print(f"   迭代次数: {result.iterations}")
            return True
        else:
            print("❌ 完整流程测试失败")
            return False
    except Exception as e:
        print(f"❌ 错误: {e}")
        import traceback
        traceback.print_exc()
        return False


def run_all_tests():
    """运行所有测试"""
    print("\n" + "="*60)
    print("🚀 开始运行测试套件")
    print("="*60)
    
    tests = [
        ("API连接", test_api_connection),
        ("意图理解", test_intent_understanding),
        ("联网检索", test_search),
        ("描述重构", test_description_reconstruction),
        ("表情生成", test_emoji_generation),
        ("品鉴评估", test_evaluation),
        ("完整流程", test_full_generation)
    ]
    
    results = []
    for name, test_func in tests:
        try:
            result = test_func()
            results.append((name, result))
        except Exception as e:
            print(f"\n❌ 测试 '{name}' 发生异常: {e}")
            results.append((name, False))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 测试结果汇总")
    print("="*60)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for name, result in results:
        status = "✅ 通过" if result else "❌ 失败"
        print(f"{name:12s}: {status}")
    
    print("-" * 60)
    print(f"总计: {passed}/{total} 通过")
    print("="*60 + "\n")
    
    return passed == total


def main():
    """主函数"""
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("\n⚠️  请设置OPENAI_API_KEY环境变量")
        print("示例: export OPENAI_API_KEY='your-api-key'")
        sys.exit(1)
    
    # 运行测试
    success = run_all_tests()
    
    # 返回退出码
    sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()

