#!/usr/bin/env python3
"""
表情生成器 - 通过多AI代理协作生成符合用户需求的表情
"""

import os
import json
import requests
from typing import Dict, List, Tuple
from dataclasses import dataclass
import time


@dataclass
class GenerationResult:
    """生成结果"""
    emoji: str
    score: float
    description: str
    iterations: int
    feedback: str


class EmojiGenerator:
    """表情生成器主类"""
    
    def __init__(self, api_key: str = None, api_base: str = None):
        """
        初始化表情生成器
        
        Args:
            api_key: OpenAI API密钥
            api_base: API基础URL（可选，用于兼容其他API）
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.api_base = api_base or os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
        
        if not self.api_key:
            raise ValueError("请设置OPENAI_API_KEY环境变量或传入api_key参数")
    
    def _call_ai(self, system_prompt: str, user_prompt: str, temperature: float = 0.7) -> str:
        """
        调用AI API
        
        Args:
            system_prompt: 系统提示词
            user_prompt: 用户提示词
            temperature: 温度参数
            
        Returns:
            AI响应内容
        """
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "gpt-4",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "temperature": temperature
        }
        
        try:
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
        except Exception as e:
            print(f"AI调用错误: {e}")
            raise
    
    def _web_search(self, query: str) -> List[Dict[str, str]]:
        """
        模拟联网检索（实际项目中可接入真实搜索API）
        
        Args:
            query: 搜索查询
            
        Returns:
            搜索结果列表
        """
        # 这里使用AI来模拟搜索结果，实际项目中应该接入真实的搜索API
        system_prompt = """你是一个搜索引擎模拟器。根据用户的查询，返回3-5个相关的搜索结果。
每个结果应该包含标题和简短描述。以JSON格式返回，格式如下：
[
    {"title": "标题1", "description": "描述1"},
    {"title": "标题2", "description": "描述2"}
]"""
        
        user_prompt = f"搜索查询: {query}\n请返回相关的搜索结果（关于表情、情绪、文化含义等）"
        
        try:
            response = self._call_ai(system_prompt, user_prompt, temperature=0.5)
            # 尝试解析JSON
            results = json.loads(response)
            return results
        except:
            # 如果解析失败，返回默认结果
            return [{"title": "相关信息", "description": response}]
    
    def understand_intent(self, user_input: str) -> str:
        """
        意图理解代理 - 深入理解用户需求
        
        Args:
            user_input: 用户输入
            
        Returns:
            理解后的意图描述
        """
        print("\n🤔 [意图理解代理] 正在分析用户需求...")
        
        system_prompt = """你是一个专业的意图理解专家。你的任务是深入理解用户想要生成的表情背后的真实需求。
分析以下方面：
1. 用户想表达的核心情绪或概念
2. 可能的使用场景
3. 目标受众
4. 文化背景和语境
5. 视觉元素的关键特征

请用简洁但全面的方式描述你的理解。"""
        
        user_prompt = f"用户需求: {user_input}\n\n请深入分析并描述用户的真实意图。"
        
        intent = self._call_ai(system_prompt, user_prompt)
        print(f"✅ 意图理解: {intent[:100]}...")
        return intent
    
    def search_and_enrich(self, intent: str) -> str:
        """
        联网检索并丰富理解
        
        Args:
            intent: 理解的意图
            
        Returns:
            丰富后的信息
        """
        print("\n🔍 [联网检索] 正在搜索相关信息...")
        
        # 生成搜索查询
        search_query = self._call_ai(
            "你是搜索查询生成专家。根据意图生成1-2个最相关的搜索关键词。",
            f"意图: {intent}\n\n生成搜索关键词（只返回关键词，用空格分隔）："
        )
        
        print(f"🔎 搜索关键词: {search_query}")
        
        # 执行搜索
        search_results = self._web_search(search_query)
        
        # 整合搜索结果
        results_text = "\n".join([
            f"- {r['title']}: {r['description']}" 
            for r in search_results
        ])
        
        print(f"✅ 找到 {len(search_results)} 条相关信息")
        return results_text

    def reconstruct_description(self, intent: str, search_results: str, user_input: str) -> str:
        """
        描述重构代理 - 将理解封装成新的描述

        Args:
            intent: 原始意图理解
            search_results: 搜索结果
            user_input: 用户原始输入

        Returns:
            重构后的表情描述
        """
        print("\n✨ [描述重构代理] 正在生成优化描述...")

        system_prompt = """你是一个创意描述专家，专门为表情生成创作精准的描述。
基于用户意图和搜索到的信息，创建一个详细的表情描述，包括：
1. 核心视觉元素（形状、颜色、符号）
2. 情感表达（面部表情、肢体语言）
3. 风格特征（可爱、专业、幽默等）
4. 文化元素（如果相关）

描述应该具体、生动，便于AI理解并生成表情。"""

        user_prompt = f"""用户原始需求: {user_input}

意图分析:
{intent}

相关信息:
{search_results}

请创建一个优化的表情描述，用于指导AI生成表情。"""

        description = self._call_ai(system_prompt, user_prompt, temperature=0.8)
        print(f"✅ 优化描述: {description[:100]}...")
        return description

    def generate_emoji(self, description: str) -> str:
        """
        表情生成代理 - 使用AI生成表情

        Args:
            description: 表情描述

        Returns:
            生成的表情（Unicode或文本组合）
        """
        print("\n🎨 [表情生成代理] 正在生成表情...")

        system_prompt = """你是一个表情创作大师。根据描述生成合适的表情。

你可以：
1. 使用现有的Unicode表情符号（如 😊 🎉 ❤️ 等）
2. 组合多个表情创造新意（如 🔥💪 表示"燃烧的力量"）
3. 使用文本艺术（如 (╯°□°）╯︵ ┻━┻ ）
4. 创造性地组合符号和表情

请只返回表情本身，不要有其他解释。如果需要组合，最多使用3-4个元素。"""

        user_prompt = f"描述: {description}\n\n请生成表情："

        emoji = self._call_ai(system_prompt, user_prompt, temperature=0.9)
        print(f"✅ 生成表情: {emoji}")
        return emoji

    def evaluate_emoji(self, emoji: str, original_request: str, description: str) -> Tuple[float, str]:
        """
        品鉴大师代理 - 评估表情质量

        Args:
            emoji: 生成的表情
            original_request: 用户原始需求
            description: 优化后的描述

        Returns:
            (评分, 反馈意见)
        """
        print("\n👨‍🍳 [品鉴大师] 正在评估表情...")

        system_prompt = """你是一位严格的表情品鉴大师。你需要评估生成的表情是否符合用户需求。

评分标准（0-10分）：
1. 相关性（3分）：表情是否准确表达了用户的意图
2. 创意性（2分）：表情是否有创意和新意
3. 美观性（2分）：表情是否视觉上令人愉悦
4. 实用性（2分）：表情是否容易理解和使用
5. 文化适配（1分）：表情是否符合文化背景

请以JSON格式返回评估结果：
{
    "score": 8.5,
    "feedback": "详细的反馈意见，包括优点和改进建议"
}"""

        user_prompt = f"""用户原始需求: {original_request}

优化描述: {description}

生成的表情: {emoji}

请评估这个表情并给出评分和反馈。"""

        try:
            response = self._call_ai(system_prompt, user_prompt, temperature=0.3)
            result = json.loads(response)
            score = result.get("score", 0)
            feedback = result.get("feedback", "无反馈")
        except:
            # 如果JSON解析失败，尝试从文本中提取
            score = 5.0
            feedback = response

        print(f"✅ 评分: {score}/10")
        print(f"📝 反馈: {feedback[:100]}...")

        return score, feedback

    def generate(self, user_input: str, min_score: float = 7.0, max_iterations: int = 3) -> GenerationResult:
        """
        完整的表情生成流程

        Args:
            user_input: 用户输入的需求
            min_score: 最低可接受分数
            max_iterations: 最大迭代次数

        Returns:
            生成结果
        """
        print(f"\n{'='*60}")
        print(f"🚀 开始生成表情")
        print(f"📝 用户需求: {user_input}")
        print(f"🎯 目标分数: {min_score}/10")
        print(f"{'='*60}")

        # 步骤1: 理解意图
        intent = self.understand_intent(user_input)

        # 步骤2: 联网检索
        search_results = self.search_and_enrich(intent)

        # 步骤3: 重构描述
        description = self.reconstruct_description(intent, search_results, user_input)

        # 迭代生成和评估
        best_emoji = ""
        best_score = 0.0
        best_feedback = ""

        for iteration in range(1, max_iterations + 1):
            print(f"\n{'─'*60}")
            print(f"🔄 第 {iteration}/{max_iterations} 次迭代")
            print(f"{'─'*60}")

            # 步骤4: 生成表情
            if iteration > 1:
                # 后续迭代时，根据反馈优化描述
                description = self._refine_description(description, best_feedback)

            emoji = self.generate_emoji(description)

            # 步骤5: 评估表情
            score, feedback = self.evaluate_emoji(emoji, user_input, description)

            # 更新最佳结果
            if score > best_score:
                best_emoji = emoji
                best_score = score
                best_feedback = feedback

            # 如果达到目标分数，提前结束
            if score >= min_score:
                print(f"\n🎉 达到目标分数！")
                break

        # 返回结果
        result = GenerationResult(
            emoji=best_emoji,
            score=best_score,
            description=description,
            iterations=iteration,
            feedback=best_feedback
        )

        self._print_final_result(result, user_input)
        return result

    def _refine_description(self, description: str, feedback: str) -> str:
        """
        根据反馈优化描述

        Args:
            description: 当前描述
            feedback: 品鉴反馈

        Returns:
            优化后的描述
        """
        print("\n🔧 [描述优化] 根据反馈优化描述...")

        system_prompt = """你是描述优化专家。根据品鉴大师的反馈，改进表情描述。
保留原描述的核心要素，但根据反馈进行针对性优化。"""

        user_prompt = f"""当前描述:
{description}

品鉴反馈:
{feedback}

请优化描述以获得更好的表情生成效果。"""

        refined = self._call_ai(system_prompt, user_prompt, temperature=0.7)
        return refined

    def _print_final_result(self, result: GenerationResult, user_input: str):
        """打印最终结果"""
        print(f"\n{'='*60}")
        print(f"✨ 生成完成！")
        print(f"{'='*60}")
        print(f"📝 原始需求: {user_input}")
        print(f"🎨 生成表情: {result.emoji}")
        print(f"⭐ 最终评分: {result.score}/10")
        print(f"🔄 迭代次数: {result.iterations}")
        print(f"\n📋 品鉴反馈:")
        print(f"{result.feedback}")
        print(f"{'='*60}\n")


def main():
    """主函数 - 示例用法"""
    import sys

    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  请设置OPENAI_API_KEY环境变量")
        print("示例: export OPENAI_API_KEY='your-api-key'")
        sys.exit(1)

    # 创建生成器
    generator = EmojiGenerator()

    # 示例：从命令行参数获取用户需求
    if len(sys.argv) > 1:
        user_request = " ".join(sys.argv[1:])
    else:
        # 默认示例
        user_request = input("请输入你想要的表情描述: ")

    # 生成表情
    result = generator.generate(
        user_input=user_request,
        min_score=7.0,  # 最低7分才接受
        max_iterations=3  # 最多迭代3次
    )

    # 输出结果
    print(f"\n🎉 最终表情: {result.emoji}")
    print(f"💾 可以直接复制使用！")


if __name__ == "__main__":
    main()

