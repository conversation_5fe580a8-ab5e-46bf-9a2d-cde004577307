# 🎨 AI表情生成器

一个基于多AI代理协作的智能表情生成系统，通过深度理解用户意图、联网检索、创意生成和专业品鉴，生成最符合需求的表情。

## ✨ 核心特性

### 🤖 多AI代理协作

1. **意图理解代理** 🤔
   - 深入分析用户需求
   - 理解情绪、场景、文化背景
   - 提取关键视觉元素

2. **联网检索模块** 🔍
   - 搜索相关词汇和概念
   - 丰富表情的文化内涵
   - 获取最新流行趋势

3. **描述重构代理** ✨
   - 将理解封装成精准描述
   - 优化视觉元素表达
   - 提供创意指导

4. **表情生成代理** 🎨
   - 使用AI创造性生成表情
   - 支持Unicode表情、组合表情、文本艺术
   - 多样化的表达方式

5. **品鉴大师代理** 👨‍🍳
   - 专业评分系统（0-10分）
   - 多维度评估（相关性、创意性、美观性、实用性、文化适配）
   - 提供改进建议

### 🔄 迭代优化机制

- 自动迭代生成，直到达到目标分数
- 根据品鉴反馈持续优化
- 最多支持自定义迭代次数

## 🚀 快速开始

### 1. 安装依赖

```bash
pip install -r requirements.txt
```

### 2. 配置API密钥

```bash
# 复制环境变量模板
cp .env.example .env

# 编辑.env文件，填入你的OpenAI API密钥
export OPENAI_API_KEY='your-api-key-here'
```

### 3. 运行生成器

```bash
# 交互式模式
python emoji_generator.py

# 命令行模式
python emoji_generator.py "开心到飞起"
python emoji_generator.py "加班到深夜的程序员"
python emoji_generator.py "周末躺平"
```

## 📖 使用示例

### 示例1：基础使用

```python
from emoji_generator import EmojiGenerator

# 创建生成器
generator = EmojiGenerator(api_key="your-api-key")

# 生成表情
result = generator.generate(
    user_input="开心到飞起",
    min_score=7.0,      # 最低接受分数
    max_iterations=3    # 最大迭代次数
)

print(f"生成的表情: {result.emoji}")
print(f"评分: {result.score}/10")
```

### 示例2：自定义配置

```python
# 使用自定义API端点（兼容OpenAI格式的其他服务）
generator = EmojiGenerator(
    api_key="your-api-key",
    api_base="https://your-api-endpoint.com/v1"
)

result = generator.generate(
    user_input="赛博朋克风格的机器人",
    min_score=8.0,      # 更高的质量要求
    max_iterations=5    # 更多迭代次数
)
```

## 🎯 工作流程

```
用户输入
    ↓
[意图理解代理] 分析需求
    ↓
[联网检索] 搜索相关信息
    ↓
[描述重构代理] 生成优化描述
    ↓
    ┌─────────────────┐
    │  迭代循环开始    │
    └─────────────────┘
    ↓
[表情生成代理] 创造表情
    ↓
[品鉴大师代理] 评分反馈
    ↓
达到目标分数？
    ├─ 是 → 输出结果
    └─ 否 → 优化描述，继续迭代
```

## 📊 评分标准

品鉴大师使用以下标准评估表情（总分10分）：

- **相关性** (3分): 表情是否准确表达用户意图
- **创意性** (2分): 表情是否有创意和新意
- **美观性** (2分): 表情是否视觉上令人愉悦
- **实用性** (2分): 表情是否容易理解和使用
- **文化适配** (1分): 表情是否符合文化背景

## 🛠️ 高级功能

### 自定义评分阈值

```python
# 要求更高质量的表情
result = generator.generate(
    user_input="专业的商务表情",
    min_score=8.5  # 高标准
)
```

### 查看详细过程

生成器会自动打印详细的处理过程：
- 意图理解结果
- 搜索关键词和结果
- 优化后的描述
- 每次迭代的评分和反馈

## 🔧 技术架构

- **语言**: Python 3.7+
- **AI模型**: GPT-4 (可配置其他兼容模型)
- **依赖**: requests

## 📝 注意事项

1. 需要有效的OpenAI API密钥或兼容的API服务
2. 生成过程会调用多次AI API，请注意API使用成本
3. 联网检索功能当前使用AI模拟，可替换为真实搜索API
4. 建议设置合理的迭代次数，避免过度消耗

## 🤝 贡献

欢迎提交Issue和Pull Request！

## 📄 许可证

MIT License

