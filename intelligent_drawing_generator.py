#!/usr/bin/env python3
"""
智能绘图生成器
理解用户不确定输入 → 联网搜索理解真实意图 → 生成绘图提示词
"""

import os
import json
import requests
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import time


@dataclass
class UnderstandingResult:
    """理解分析结果"""
    original_input: str
    core_meaning: str
    emotional_context: str
    visual_elements: List[str]
    cultural_context: str
    scene_description: str
    search_keywords: List[str]


@dataclass
class DrawingPrompt:
    """绘图提示词结果"""
    main_prompt: str
    detailed_description: str
    style_tags: List[str]
    color_scheme: str
    mood_keywords: str
    technical_specs: str
    alternative_versions: List[str]


class IntelligentDrawingGenerator:
    """智能绘图生成器主类"""
    
    def __init__(self, api_key: str = None, api_base: str = None):
        """
        初始化生成器
        
        Args:
            api_key: OpenAI API密钥
            api_base: API基础URL
        """
        self.api_key = api_key or os.getenv("OPENAI_API_KEY")
        self.api_base = api_base or os.getenv("OPENAI_API_BASE", "https://api.openai.com/v1")
        
        if not self.api_key:
            raise ValueError("请设置OPENAI_API_KEY环境变量或传入api_key参数")
    
    def _call_ai(self, system_prompt: str, user_prompt: str, temperature: float = 0.7) -> str:
        """调用AI API"""
        headers = {
            "Authorization": f"Bearer {self.api_key}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": "gpt-4",
            "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
            ],
            "temperature": temperature
        }
        
        try:
            response = requests.post(
                f"{self.api_base}/chat/completions",
                headers=headers,
                json=data,
                timeout=30
            )
            response.raise_for_status()
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
        except Exception as e:
            print(f"AI调用错误: {e}")
            raise
    
    def understand_user_input(self, user_input: str) -> UnderstandingResult:
        """
        深度理解用户输入
        分析用户真实想表达的内容
        """
        print(f"\n🤔 [理解分析] 正在分析用户输入: '{user_input}'")
        
        system_prompt = """你是一个专业的用户意图理解专家。用户可能会说任何话，你需要深度分析他们真正想表达什么。

分析维度：
1. 核心含义：用户真正想表达的核心概念
2. 情感背景：隐含的情绪、感受、心境
3. 视觉元素：可能对应的视觉形象、符号、场景
4. 文化语境：涉及的文化背景、流行元素、社会现象
5. 场景描述：具体的情境、环境、氛围

请以JSON格式返回分析结果：
{
    "core_meaning": "核心含义",
    "emotional_context": "情感背景",
    "visual_elements": ["视觉元素1", "视觉元素2"],
    "cultural_context": "文化语境",
    "scene_description": "场景描述",
    "search_keywords": ["搜索关键词1", "搜索关键词2"]
}"""
        
        user_prompt = f"用户输入: '{user_input}'\n\n请深度分析这个输入的真实含义。"
        
        try:
            response = self._call_ai(system_prompt, user_prompt, temperature=0.6)
            analysis = json.loads(response)
            
            result = UnderstandingResult(
                original_input=user_input,
                core_meaning=analysis.get("core_meaning", ""),
                emotional_context=analysis.get("emotional_context", ""),
                visual_elements=analysis.get("visual_elements", []),
                cultural_context=analysis.get("cultural_context", ""),
                scene_description=analysis.get("scene_description", ""),
                search_keywords=analysis.get("search_keywords", [])
            )
            
            print(f"✅ 核心含义: {result.core_meaning}")
            print(f"✅ 情感背景: {result.emotional_context}")
            print(f"✅ 搜索关键词: {', '.join(result.search_keywords)}")
            
            return result
            
        except json.JSONDecodeError:
            # 如果JSON解析失败，使用备用方案
            print("⚠️ JSON解析失败，使用备用分析方案")
            return UnderstandingResult(
                original_input=user_input,
                core_meaning=response[:100] if 'response' in locals() else user_input,
                emotional_context="需要进一步分析",
                visual_elements=[],
                cultural_context="通用",
                scene_description="",
                search_keywords=[user_input]
            )
    
    def web_search_for_context(self, understanding: UnderstandingResult) -> str:
        """
        联网搜索获取上下文信息
        """
        print(f"\n🔍 [联网搜索] 搜索相关信息...")
        
        # 生成搜索查询
        search_queries = understanding.search_keywords[:3]  # 最多搜索3个关键词
        all_results = []
        
        for query in search_queries:
            print(f"🔎 搜索: {query}")
            
            # 使用AI模拟搜索结果（实际项目中应接入真实搜索API）
            system_prompt = """你是一个搜索引擎，根据查询返回相关信息。
返回格式：简洁的信息描述，包含相关的文化背景、流行趋势、视觉特征等。"""
            
            user_prompt = f"搜索查询: {query}\n请返回相关的背景信息、文化含义、视觉特征等。"
            
            try:
                search_result = self._call_ai(system_prompt, user_prompt, temperature=0.5)
                all_results.append(f"关于'{query}': {search_result}")
            except:
                all_results.append(f"关于'{query}': 相关信息")
        
        combined_results = "\n\n".join(all_results)
        print(f"✅ 获取到 {len(search_queries)} 个搜索结果")
        
        return combined_results
