#!/usr/bin/env python3
"""
表情生成器演示脚本
展示各种使用场景
"""

import os
from emoji_generator import EmojiGenerator


def demo_basic():
    """基础演示"""
    print("\n" + "="*60)
    print("📌 演示1: 基础使用")
    print("="*60)
    
    generator = EmojiGenerator()
    
    # 简单的情绪表达
    result = generator.generate(
        user_input="开心到飞起",
        min_score=7.0,
        max_iterations=3
    )
    
    return result


def demo_complex():
    """复杂场景演示"""
    print("\n" + "="*60)
    print("📌 演示2: 复杂场景")
    print("="*60)
    
    generator = EmojiGenerator()
    
    # 复杂的场景描述
    result = generator.generate(
        user_input="周五下班后和朋友聚会，喝着啤酒聊天的轻松氛围",
        min_score=7.5,
        max_iterations=3
    )
    
    return result


def demo_cultural():
    """文化相关演示"""
    print("\n" + "="*60)
    print("📌 演示3: 文化元素")
    print("="*60)
    
    generator = EmojiGenerator()
    
    # 包含文化元素
    result = generator.generate(
        user_input="中国传统节日春节的喜庆氛围",
        min_score=7.0,
        max_iterations=3
    )
    
    return result


def demo_professional():
    """专业场景演示"""
    print("\n" + "="*60)
    print("📌 演示4: 专业场景")
    print("="*60)
    
    generator = EmojiGenerator()
    
    # 专业/商务场景
    result = generator.generate(
        user_input="项目成功上线，团队庆祝",
        min_score=8.0,
        max_iterations=3
    )
    
    return result


def demo_creative():
    """创意表达演示"""
    print("\n" + "="*60)
    print("📌 演示5: 创意表达")
    print("="*60)
    
    generator = EmojiGenerator()
    
    # 抽象概念
    result = generator.generate(
        user_input="代码重构后的清爽感觉",
        min_score=7.0,
        max_iterations=3
    )
    
    return result


def demo_batch():
    """批量生成演示"""
    print("\n" + "="*60)
    print("📌 演示6: 批量生成")
    print("="*60)
    
    generator = EmojiGenerator()
    
    requests = [
        "早安",
        "晚安",
        "加油",
        "辛苦了",
        "周末愉快"
    ]
    
    results = []
    for req in requests:
        print(f"\n🎯 生成: {req}")
        result = generator.generate(
            user_input=req,
            min_score=6.5,  # 批量生成时可以降低标准
            max_iterations=2  # 减少迭代次数以提高速度
        )
        results.append((req, result.emoji, result.score))
    
    # 汇总结果
    print("\n" + "="*60)
    print("📊 批量生成结果汇总")
    print("="*60)
    for req, emoji, score in results:
        print(f"{req:12s} → {emoji:20s} (评分: {score:.1f}/10)")
    
    return results


def interactive_mode():
    """交互式模式"""
    print("\n" + "="*60)
    print("🎮 交互式表情生成器")
    print("="*60)
    print("输入 'quit' 或 'exit' 退出")
    print("="*60 + "\n")
    
    generator = EmojiGenerator()
    
    while True:
        try:
            user_input = input("\n💭 请描述你想要的表情: ").strip()
            
            if user_input.lower() in ['quit', 'exit', 'q']:
                print("\n👋 再见！")
                break
            
            if not user_input:
                print("⚠️  请输入有效的描述")
                continue
            
            # 询问质量要求
            quality = input("🎯 质量要求 (1-10，默认7): ").strip()
            try:
                min_score = float(quality) if quality else 7.0
                min_score = max(1.0, min(10.0, min_score))
            except:
                min_score = 7.0
            
            # 生成表情
            result = generator.generate(
                user_input=user_input,
                min_score=min_score,
                max_iterations=3
            )
            
            print(f"\n✨ 你的表情: {result.emoji}")
            print(f"📋 可以直接复制使用！\n")
            
        except KeyboardInterrupt:
            print("\n\n👋 再见！")
            break
        except Exception as e:
            print(f"\n❌ 错误: {e}")
            print("请重试...\n")


def main():
    """主函数"""
    # 检查API密钥
    if not os.getenv("OPENAI_API_KEY"):
        print("⚠️  请设置OPENAI_API_KEY环境变量")
        print("示例: export OPENAI_API_KEY='your-api-key'")
        return
    
    print("\n🎨 AI表情生成器 - 演示程序")
    print("="*60)
    print("选择演示模式:")
    print("1. 基础使用")
    print("2. 复杂场景")
    print("3. 文化元素")
    print("4. 专业场景")
    print("5. 创意表达")
    print("6. 批量生成")
    print("7. 交互式模式")
    print("0. 运行所有演示")
    print("="*60)
    
    choice = input("\n请选择 (0-7): ").strip()
    
    demos = {
        '1': demo_basic,
        '2': demo_complex,
        '3': demo_cultural,
        '4': demo_professional,
        '5': demo_creative,
        '6': demo_batch,
        '7': interactive_mode
    }
    
    if choice == '0':
        # 运行所有演示（除了交互式）
        for key in ['1', '2', '3', '4', '5', '6']:
            demos[key]()
    elif choice in demos:
        demos[choice]()
    else:
        print("❌ 无效选择")


if __name__ == "__main__":
    main()

